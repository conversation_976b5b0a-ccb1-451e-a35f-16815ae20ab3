'use client'
import { useState, useEffect } from 'react'
import { isToday, isTomorrow } from 'date-fns'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { usePathname, useRouter } from 'next/navigation'
import { H2 } from '@/components/_common/ui'
import { Button } from '@/components/ui/button'
import { useBooking } from '@/context/useBookingCounseling'
import { DatePicker } from '@/components/ui/DatePicker'

// Define types
interface BreakdownAvailability {
  durationInMinute: number
  price: number
  schedule?: ScheduleItem[]
}

interface ScheduleItem {
  date: string
  timezone: string
  times: TimeSlot[]
}

interface TimeSlot {
  time: string
  dateTimeWithTimezone: string
  isAvailable: boolean
}

interface Psychologist {
  id: string
  fullName: string
  profilePhoto?: string
  nickname?: string
  specialization?: string[]
  breakdownAvailability?: BreakdownAvailability[]
}

interface FormattedDate {
  day: string
  date: string
  month: string
  fullDate: string
}

export interface BookingSectionProps {
  user: any
  psychologist: Psychologist
  availabilityDate: string[] | null
  onDateChange?: (date: string) => void
}

export default function BookingSection({
  psychologist,
  availabilityDate,
  user,
  onDateChange,
}: BookingSectionProps) {
  const [selectedDuration, setSelectedDuration] = useState<number>(60)
  const [selectedDate, setSelectedDate] = useState('')
  const [selectedTime, setSelectedTime] = useState('')
  const [formattedDates, setFormattedDates] = useState<FormattedDate[]>([])
  const [availableTimes, setAvailableTimes] = useState<string[]>([])
  const [selectedMedia, setSelectedMedia] = useState<'Call' | 'VideoCall'>('VideoCall')
  const [pickerDate, setPickerDate] = useState<Date | undefined>(undefined)
  const [visibleDates, setVisibleDates] = useState<FormattedDate[]>([])
  const router = useRouter()
  const pathname = usePathname()
  const { bookingState, setBookingDetails } = useBooking()

  // Function to get 4 visible dates based on selected date from picker
  const getVisibleDates = (selectedFromPicker?: Date) => {
    if (!formattedDates.length) return []

    if (!selectedFromPicker) {
      // Show first 4 dates by default
      return formattedDates.slice(0, 4)
    }

    // Format selected date from picker
    const year = selectedFromPicker.getFullYear()
    const month = String(selectedFromPicker.getMonth() + 1).padStart(2, '0')
    const day = String(selectedFromPicker.getDate()).padStart(2, '0')
    const selectedDateStr = `${year}-${month}-${day}`

    // Find index of selected date in all available dates
    const selectedIndex = formattedDates.findIndex((d) => d.fullDate.startsWith(selectedDateStr))

    if (selectedIndex === -1) {
      // If selected date not found, show first 4
      return formattedDates.slice(0, 4)
    }

    // Calculate start index to show 4 dates with selected date in the middle
    let startIndex = Math.max(0, selectedIndex - 1)

    // Adjust if we don't have enough dates after
    if (startIndex + 4 > formattedDates.length) {
      startIndex = Math.max(0, formattedDates.length - 4)
    }

    return formattedDates.slice(startIndex, startIndex + 4)
  }

  // Handler for DatePicker selection
  const handleDatePickerSelect = (date: Date | undefined) => {
    if (date && availabilityDate) {
      // Format date as YYYY-MM-DD in local timezone
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const dateStr = `${year}-${month}-${day}`

      const matchingDate = availabilityDate.find((d) => d.startsWith(dateStr))
      if (matchingDate) {
        setSelectedDate(matchingDate)
        setPickerDate(date)
        // Update visible dates based on picker selection
        setVisibleDates(getVisibleDates(date))
        // Notify parent component about date change
        onDateChange?.(dateStr)
      }
    }
  }

  // Get available dates for DatePicker
  const getAvailableDates = () => {
    if (!availabilityDate) return []
    return availabilityDate.map((dateStr) => {
      // Parse date string as local date to avoid timezone issues
      const [year, month, day] = dateStr.split('-').map(Number)
      return new Date(year, month - 1, day) // month is 0-indexed
    })
  }

  useEffect(() => {
    if (availabilityDate && availabilityDate.length > 0) {
      const days = ['Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu']
      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Agu', 'Sep', 'Okt', 'Nov', 'Des']

      const formatted = availabilityDate.map((dateStr: string, index: number) => {
        const date = new Date(dateStr)
        const day = date.getDay()
        const dayName =
          index === 0 && isToday(date) ? 'Hari ini' : index === 0 && isTomorrow(date) ? 'Besok' : days[day]

        return {
          day: dayName,
          date: date.getDate().toString(),
          month: months[date.getMonth()],
          fullDate: dateStr,
        }
      })

      setFormattedDates(formatted)

      // Initialize visible dates (first 4 by default)
      setVisibleDates(formatted.slice(0, 4))

      // If we have a booking state with a selected date, use that
      // Otherwise, select first date by default
      if (bookingState?.selectedDate) {
        // Find the matching date in our formatted dates
        const matchingDate = formatted.find((d) => d.fullDate.startsWith(bookingState.selectedDate))
        if (matchingDate) {
          setSelectedDate(matchingDate.fullDate)
        } else if (formatted.length > 0) {
          setSelectedDate(formatted[0].fullDate)
        }
      } else if (formatted.length > 0) {
        setSelectedDate(formatted[0].fullDate)
      }
    }
  }, [availabilityDate, bookingState])

  // Effect for available times - Updated to use breakdownAvailability data
  useEffect(() => {
    if (selectedDate && psychologist.breakdownAvailability) {
      const selectedDurationSchedule = psychologist.breakdownAvailability.find(
        (breakdown) => breakdown.durationInMinute === selectedDuration
      )

      if (selectedDurationSchedule?.schedule) {
        const scheduleForDate = selectedDurationSchedule.schedule.find(
          (schedule) => schedule.date === selectedDate.split('T')[0]
        )

        if (scheduleForDate?.times) {
          // Filter only available times and format them
          const availableTimeSlots = scheduleForDate.times
            .filter((slot) => slot.isAvailable)
            .map((slot) => `${slot.time} WIB`)

          setAvailableTimes(availableTimeSlots)

          // Reset selected time if previously selected time is no longer available
          if (!availableTimeSlots.find((time) => time.startsWith(selectedTime))) {
            setSelectedTime(availableTimeSlots[0]?.split(' ')[0] || '')
          }
        } else {
          setAvailableTimes([])
          setSelectedTime('')
        }
      } else {
        setAvailableTimes([])
        setSelectedTime('')
      }
    } else {
      setAvailableTimes([])
      setSelectedTime('')
    }
  }, [selectedDate, selectedDuration, psychologist.breakdownAvailability, selectedTime])

  // Set duration from booking state if available
  useEffect(() => {
    if (bookingState?.duration) {
      setSelectedDuration(bookingState.duration)
    }
  }, [bookingState])

  // Helper function to calculate end time
  const getEndTime = (startTime: string, durationMinutes: number) => {
    const [hours, minutes] = startTime.split(':').map(Number)

    let endMinutes = minutes + durationMinutes
    let endHours = hours + Math.floor(endMinutes / 60)
    endMinutes = endMinutes % 60

    return `${endHours.toString().padStart(2, '0')}:${endMinutes.toString().padStart(2, '0')}`
  }

  const handleBooking = () => {
    if (!user) {
      return router.push('/auth/login')
    }

    // Format the date and time for display
    const selectedDateObj = new Date(selectedDate)
    const dayNames = ['Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu']
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Agu', 'Sep', 'Okt', 'Nov', 'Des']

    // Ensure durationInMinutes is a number
    const durationInMinutes =
      typeof selectedDuration === 'string' ? parseInt(selectedDuration, 10) : selectedDuration

    const formattedDate = `${dayNames[selectedDateObj.getDay()]}, ${selectedDateObj.getDate()} ${
      monthNames[selectedDateObj.getMonth()]
    } ${selectedDateObj.getFullYear()}, ${selectedTime}-${getEndTime(selectedTime, durationInMinutes)} WIB (GMT+7)`

    // Calculate price based on selected duration
    let price = 149000 // Default price for 60 minutes
    if (psychologist.breakdownAvailability && psychologist.breakdownAvailability.length > 0) {
      const selectedOption = psychologist.breakdownAvailability.find(
        (option) => option.durationInMinute === durationInMinutes
      )
      if (selectedOption) {
        price = selectedOption.price
      }
    } else if (durationInMinutes === 120) {
      price = 299000 // Default price for 120 minutes
    }

    // Apply discounts - These would normally come from your application state or API
    const discount = 0
    const voucherPromo = 0
    const totalAfterDiscount = price - discount - voucherPromo
    const finalPrice = totalAfterDiscount
    // Prepare specializations for display
    const specializations = psychologist.specialization || []
    const formattedSpecializations =
      specializations.length <= 3
        ? specializations.join(', ')
        : `${specializations.slice(0, 3).join(', ')}, +${specializations.length - 3} lainnya`

    // Create ISO format for schedule time
    // Convert selected time from "HH:MM WIB" to ISO format using selectedDate as the base
    const [timeHours, timeMinutes] = selectedTime.split(':').map(Number)
    const scheduleDate = new Date(selectedDate)
    scheduleDate.setHours(timeHours, timeMinutes, 0, 0)
    const rawSchedule = scheduleDate.toISOString()

    setBookingDetails({
      psychologistId: psychologist.id || '',
      psychologistName: psychologist.fullName,
      psychologistImage: psychologist.profilePhoto || '',
      specializations: formattedSpecializations,
      selectedDate,
      selectedTime,
      formattedDate,
      rawSchedule: rawSchedule, // Properly formatted ISO string
      duration: durationInMinutes, // As a number
      method: selectedMedia,
      location: 'Online',
      price,
      discount,
      voucherPromo,
      totalAfterDiscount,
      finalPrice,
    })

    // Navigate to the konseling page
    router.push(`${pathname}/konseling`)
  }

  return (
    <div className="p-4 border border-gray-100 rounded-lg">
      <H2 className="text-lg font-semibold mb-4">Durasi Konseling</H2>
      {/* Duration Options */}
      <div className="flex gap-2 mb-6">
        {psychologist.breakdownAvailability &&
          psychologist.breakdownAvailability.map((breakdown, index) => (
            <button
              key={index}
              className={`flex-1 py-2 px-3 border rounded-md text-center font-semibold ${
                selectedDuration === breakdown.durationInMinute
                  ? 'border-main-100 bg-main-50'
                  : 'border-gray-100'
              }`}
              onClick={() => setSelectedDuration(breakdown.durationInMinute)}
            >
              <div className="font-semibold">{breakdown.durationInMinute} Menit</div>
              <div className="text-sm text-gray-500">Rp{breakdown.price.toLocaleString('id-ID')}</div>
            </button>
          ))}
        {(!psychologist.breakdownAvailability || psychologist.breakdownAvailability.length === 0) && (
          <>
            <Button
              className={`flex-1 py-2 px-3 border rounded-md text-center ${
                selectedDuration === 60 ? 'border-main-100 bg-main-50' : 'border-gray-100'
              }`}
              onClick={() => setSelectedDuration(60)}
            >
              <div>60 Menit</div>
              <div className="text-sm text-gray-500">Rp149.000</div>
            </Button>
            <Button
              variant={'outline'}
              className={`flex-1 py-2 px-3 border rounded-md text-center ${
                selectedDuration === 120 ? 'border-main-100 bg-main-50' : 'border-gray-100'
              }`}
              onClick={() => setSelectedDuration(120)}
            >
              <div>120 Menit</div>
              <div className="text-sm text-gray-500">Rp299.000</div>
            </Button>
          </>
        )}
      </div>

      {/* Date option */}
      <H2 className="text-lg font-semibold mb-4">Tanggal</H2>
      <div className="flex gap-2 mb-6 overflow-x-auto max-w-64 md:max-w-screen-md xs:max-w-screen-xs sm:max-w-screen-sm">
        {visibleDates.length > 0 ? (
          <>
            {visibleDates.map((date) => (
              <button
                key={date.fullDate}
                className={`flex flex-col items-center min-w-16 border rounded-md w-full ${
                  selectedDate === date.fullDate ? 'border-main-100 bg-main-50' : 'border-gray-100'
                }`}
                onClick={() => {
                  setSelectedDate(date.fullDate)
                  // Notify parent component about date change
                  onDateChange?.(date.fullDate.split('T')[0])
                }}
              >
                <div className="text-sm flex flex-col items-center w-full">
                  <span
                    className={`text-xs md:text-sm w-full text-center py-1 rounded-t-md font-semibold ${
                      selectedDate === date.fullDate ? 'bg-main-100 text-white' : 'border-b-gray-100 border'
                    }`}
                  >
                    {date.day}
                  </span>
                  <span className="font-semibold">{date.date}</span>
                </div>
                <div className="text-xs text-gray-500">{date.month}</div>
              </button>
            ))}
            <div className="flex-shrink-0">
              <DatePicker
                date={pickerDate}
                onSelect={handleDatePickerSelect}
                placeholder=""
                className="w-16 h-20 p-0 border rounded-md flex items-center justify-center min-w-16"
                compact={true}
                availableDates={getAvailableDates()}
              />
            </div>
          </>
        ) : (
          <div className="text-sm text-gray-500">Tidak ada tanggal yang tersedia</div>
        )}
      </div>

      {/* Time Selection */}
      <h3 className="font-semibold mb-2">Waktu</h3>
      <div className="grid grid-cols-3 gap-2 mb-6">
        {availableTimes.length > 0 ? (
          availableTimes.map((time) => (
            <button
              key={time}
              className={`py-2 px-3 border rounded-md text-center text-xs font-semibold ${
                selectedTime === time.split(' ')[0] ? 'border-main-100 bg-main-50' : 'border-gray-100'
              }`}
              onClick={() => setSelectedTime(time.split(' ')[0])}
            >
              {time}
            </button>
          ))
        ) : (
          <div className="col-span-3 text-center text-sm text-gray-500">
            {selectedDate
              ? 'Tidak ada jadwal tersedia untuk durasi yang dipilih'
              : 'Silakan pilih tanggal terlebih dahulu'}
          </div>
        )}
      </div>

      {/* Media Selection */}
      <h3 className="font-semibold mb-2">Media Konseling</h3>
      <div className="flex gap-2 mb-6">
        <button
          className={`flex-1 flex items-center justify-center gap-2 py-3 px-2 md:px-4 border rounded-md ${
            selectedMedia === 'Call' ? 'border-main-100 text-main-100 bg-main-50' : 'border-gray-100'
          }`}
          onClick={() => setSelectedMedia('Call')}
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M20.1 11.825C19.9179 11.825 19.7557 11.7583 19.6134 11.625C19.4711 11.4917 19.3833 11.325 19.35 11.125C19.1167 9.40833 18.3875 7.9375 17.1625 6.7125C15.9375 5.4875 14.4667 4.75833 12.75 4.525C12.55 4.49167 12.3833 4.40427 12.25 4.2628C12.1167 4.12133 12.05 3.9504 12.05 3.75C12.05 3.53042 12.125 3.34635 12.275 3.1978C12.425 3.04927 12.6083 2.99167 12.825 3.025C14.9448 3.26053 16.7534 4.12697 18.2507 5.6243C19.748 7.12162 20.6145 8.93018 20.85 11.05C20.8833 11.2667 20.8233 11.45 20.67 11.6C20.5167 11.75 20.3267 11.825 20.1 11.825ZM15.7962 11.825C15.6321 11.825 15.4792 11.7708 15.3375 11.6625C15.1958 11.5542 15.1 11.4083 15.05 11.225C14.8833 10.6417 14.5875 10.1375 14.1625 9.7125C13.7375 9.2875 13.2333 8.99167 12.65 8.825C12.4667 8.775 12.3208 8.68672 12.2125 8.56017C12.1042 8.43364 12.05 8.28179 12.05 8.10463C12.05 7.85154 12.1333 7.65 12.3 7.5C12.4667 7.35 12.6667 7.3 12.9 7.35C13.7993 7.55975 14.5741 7.99136 15.2245 8.64483C15.8748 9.29828 16.3167 10.075 16.55 10.975C16.6 11.2083 16.55 11.4083 16.4 11.575C16.25 11.7417 16.0487 11.825 15.7962 11.825ZM19.875 21C17.9417 21 15.9708 20.5333 13.9625 19.6C11.9542 18.6667 10.0917 17.3417 8.375 15.625C6.65833 13.9083 5.33333 12.0458 4.4 10.0375C3.46667 8.02917 3 6.05833 3 4.125C3 3.80357 3.10714 3.53571 3.32142 3.32142C3.53571 3.10714 3.80357 3 4.125 3H7.625C7.85833 3 8.05833 3.08333 8.225 3.25C8.39167 3.41667 8.50833 3.625 8.575 3.875L9.24822 7.01603C9.28274 7.25534 9.27917 7.47083 9.2375 7.6625C9.19583 7.85417 9.10642 8.01855 8.96927 8.15565L6.475 10.675C6.90833 11.4083 7.36667 12.0917 7.85 12.725C8.33333 13.3583 8.86667 13.9583 9.45 14.525C10.0667 15.1583 10.7167 15.7375 11.4 16.2625C12.0833 16.7875 12.8 17.25 13.55 17.65L15.925 15.2C16.0917 15.0167 16.2846 14.8917 16.5038 14.825C16.723 14.7583 16.9384 14.7417 17.15 14.775L20.125 15.425C20.375 15.4917 20.5833 15.6254 20.75 15.8261C20.9167 16.0268 21 16.2515 21 16.5V19.875C21 20.1964 20.8929 20.4643 20.6786 20.6786C20.4643 20.8929 20.1964 21 19.875 21ZM5.725 9.3L7.75 7.25L7.175 4.5H4.5C4.53333 5.2 4.64583 5.9375 4.8375 6.7125C5.02917 7.4875 5.325 8.35 5.725 9.3ZM14.95 18.375C15.6333 18.6917 16.375 18.95 17.175 19.15C17.975 19.35 18.75 19.4667 19.5 19.5V16.825L16.925 16.3L14.95 18.375Z"
              fill="#039EE9"
            />
          </svg>
          <div className="flex flex-col items-start">
            <span className="text-sm font-semibold">Voice Call</span>
            <span className="text-[10px] text-gray-500">Via GoogleMeet</span>
          </div>
        </button>
        <button
          className={`flex-1 flex items-center justify-center gap-2 py-3 px-2 md:px-4 border rounded-md ${
            selectedMedia === 'VideoCall' ? 'border-main-100 text-main-100 bg-main-50' : 'border-gray-100'
          }`}
          onClick={() => setSelectedMedia('VideoCall')}
        >
          <svg
            className="text-main-100"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10.0544 16C10.2681 16 10.4458 15.9281 10.5875 15.7844C10.7292 15.6406 10.8 15.4625 10.8 15.25V12.7H13.35C13.5625 12.7 13.7406 12.6277 13.8844 12.4831C14.0281 12.3385 14.1 12.1594 14.1 11.9456C14.1 11.7319 14.0281 11.5542 13.8844 11.4125C13.7406 11.2708 13.5625 11.2 13.35 11.2H10.8V8.65C10.8 8.4375 10.7277 8.25938 10.5831 8.11562C10.4385 7.97187 10.2594 7.9 10.0456 7.9C9.83188 7.9 9.65417 7.97187 9.5125 8.11562C9.37083 8.25938 9.3 8.4375 9.3 8.65V11.2H6.75C6.5375 11.2 6.35938 11.2723 6.21563 11.4169C6.07188 11.5615 6 11.7406 6 11.9544C6 12.1681 6.07188 12.3458 6.21563 12.4875C6.35938 12.6292 6.5375 12.7 6.75 12.7H9.3V15.25C9.3 15.4625 9.37229 15.6406 9.51687 15.7844C9.66147 15.9281 9.84064 16 10.0544 16ZM3.5 20C3.1 20 2.75 19.85 2.45 19.55C2.15 19.25 2 18.9 2 18.5V5.5C2 5.1 2.15 4.75 2.45 4.45C2.75 4.15 3.1 4 3.5 4H16.5C16.9 4 17.25 4.15 17.55 4.45C17.85 4.75 18 5.1 18 5.5V10.875L21.35 7.525C21.4833 7.39167 21.625 7.35833 21.775 7.425C21.925 7.49167 22 7.60833 22 7.775V16.225C22 16.3917 21.925 16.5083 21.775 16.575C21.625 16.6417 21.4833 16.6083 21.35 16.475L18 13.125V18.5C18 18.9 17.85 19.25 17.55 19.55C17.25 19.85 16.9 20 16.5 20H3.5ZM3.5 18.5H16.5V5.5H3.5V18.5Z"
              fill="#039EE9"
            />
          </svg>
          <div className="flex flex-col items-start">
            <span className="text-sm font-semibold">Video Call</span>
            <span className="text-[10px] text-gray-500">Via GoogleMeet</span>
          </div>
        </button>
      </div>

      {/* Book Button */}
      <ButtonPrimary
        variant="contained"
        className="w-full py-3 rounded-md font-semibold hover:bg-main-600 transition-colors"
        onClick={handleBooking}
        disabled={!selectedTime}
      >
        Buat Jadwal
      </ButtonPrimary>
    </div>
  )
}
